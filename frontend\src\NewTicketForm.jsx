import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import "./App.css";

const BACKEND_URL = "http://localhost:8000";

export default function NewTicketForm({ token }) {
  const navigate = useNavigate();
  const accessToken = token || localStorage.getItem("access");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");

  const [formData, setFormData] = useState({
    productType: "",
    purchasedFrom: "",
    yearOfPurchase: "",
    productName: "",
    model: "",
    serialNo: "",
    operatingSystem: "",
  });

  const productTypeOptions = ["Camera", "Frame Grabber", "Accessories", "Software"];

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    if (error) setError("");
  };

  const validateForm = () => {
    const requiredFields = [
      'productType', 'purchasedFrom', 'yearOfPurchase', 
      'productName', 'model', 'serialNo', 'operatingSystem'
    ];
    
    for (let field of requiredFields) {
      if (!formData[field] || formData[field].trim() === "") {
        return `Please fill in the ${field.replace(/([A-Z])/g, ' $1').toLowerCase()} field.`;
      }
    }

    if (!productTypeOptions.includes(formData.productType)) {
      return "Please select a valid product type.";
    }

    const currentYear = new Date().getFullYear();
    const year = parseInt(formData.yearOfPurchase);
    if (isNaN(year) || year < 1990 || year > currentYear) {
      return `Please enter a valid year between 1990 and ${currentYear}.`;
    }

    return null;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    const validationError = validateForm();
    if (validationError) {
      setError(validationError);
      return;
    }

    setLoading(true);
    setError("");

    try {
      const response = await fetch(`${BACKEND_URL}/api/create_ticket/`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${accessToken}`,
        },
        body: JSON.stringify({
          product_type: formData.productType,
          purchased_from: formData.purchasedFrom,
          year_of_purchase: formData.yearOfPurchase,
          product_name: formData.productName,
          model: formData.model,
          serial_no: formData.serialNo,
          operating_system: formData.operatingSystem,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        // Redirect to chatbot with the new ticket
        navigate(`/chatbot/${data.ticket_number}`);
      } else {
        setError(data.message || "Failed to create ticket. Please try again.");
      }
    } catch (err) {
      console.error("Error creating ticket:", err);
      setError("Network error. Please check your connection and try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    navigate("/actions");
  };

  return (
    <div style={{ 
      padding: "40px", 
      maxWidth: "600px", 
      margin: "0 auto", 
      fontFamily: "Arial, sans-serif"
    }}>
      <h1 style={{ 
        color: "#333", 
        marginBottom: "20px",
        textAlign: "center"
      }}>
        Create New Support Ticket
      </h1>
      
      <p style={{ 
        fontSize: "1.1rem", 
        color: "#666", 
        marginBottom: "30px",
        textAlign: "center"
      }}>
        Please provide your product details below. After submitting, you'll be able to describe your issue in the chat.
      </p>

      {error && (
        <div style={{
          backgroundColor: "#ffebee",
          color: "#c62828",
          padding: "12px",
          borderRadius: "4px",
          marginBottom: "20px",
          border: "1px solid #ef5350"
        }}>
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit} style={{ display: "flex", flexDirection: "column", gap: "20px" }}>
        
        {/* Product Type */}
        <div>
          <label style={{ display: "block", marginBottom: "8px", fontWeight: "bold", color: "#333" }}>
            Product Type *
          </label>
          <select
            name="productType"
            value={formData.productType}
            onChange={handleInputChange}
            required
            style={{
              width: "100%",
              padding: "12px",
              border: "1px solid #ddd",
              borderRadius: "4px",
              fontSize: "16px"
            }}
          >
            <option value="">Select Product Type</option>
            {productTypeOptions.map(option => (
              <option key={option} value={option}>{option}</option>
            ))}
          </select>
        </div>

        {/* Purchased From */}
        <div>
          <label style={{ display: "block", marginBottom: "8px", fontWeight: "bold", color: "#333" }}>
            Purchased From *
          </label>
          <input
            type="text"
            name="purchasedFrom"
            value={formData.purchasedFrom}
            onChange={handleInputChange}
            placeholder="e.g., Online Solutions, Amazon, Direct from manufacturer"
            required
            style={{
              width: "100%",
              padding: "12px",
              border: "1px solid #ddd",
              borderRadius: "4px",
              fontSize: "16px"
            }}
          />
        </div>

        {/* Year of Purchase */}
        <div>
          <label style={{ display: "block", marginBottom: "8px", fontWeight: "bold", color: "#333" }}>
            Year of Purchase *
          </label>
          <input
            type="number"
            name="yearOfPurchase"
            value={formData.yearOfPurchase}
            onChange={handleInputChange}
            placeholder="e.g., 2023"
            min="1990"
            max={new Date().getFullYear()}
            required
            style={{
              width: "100%",
              padding: "12px",
              border: "1px solid #ddd",
              borderRadius: "4px",
              fontSize: "16px"
            }}
          />
        </div>

        {/* Product Name */}
        <div>
          <label style={{ display: "block", marginBottom: "8px", fontWeight: "bold", color: "#333" }}>
            Product Name *
          </label>
          <input
            type="text"
            name="productName"
            value={formData.productName}
            onChange={handleInputChange}
            placeholder="e.g., Genie Nano, Falcon4-CLHS"
            required
            style={{
              width: "100%",
              padding: "12px",
              border: "1px solid #ddd",
              borderRadius: "4px",
              fontSize: "16px"
            }}
          />
        </div>

        {/* Model */}
        <div>
          <label style={{ display: "block", marginBottom: "8px", fontWeight: "bold", color: "#333" }}>
            Model *
          </label>
          <input
            type="text"
            name="model"
            value={formData.model}
            onChange={handleInputChange}
            placeholder="e.g., C2590, M1920"
            required
            style={{
              width: "100%",
              padding: "12px",
              border: "1px solid #ddd",
              borderRadius: "4px",
              fontSize: "16px"
            }}
          />
        </div>

        {/* Serial Number */}
        <div>
          <label style={{ display: "block", marginBottom: "8px", fontWeight: "bold", color: "#333" }}>
            Serial Number *
          </label>
          <input
            type="text"
            name="serialNo"
            value={formData.serialNo}
            onChange={handleInputChange}
            placeholder="e.g., SN123456789"
            required
            style={{
              width: "100%",
              padding: "12px",
              border: "1px solid #ddd",
              borderRadius: "4px",
              fontSize: "16px"
            }}
          />
        </div>

        {/* Operating System */}
        <div>
          <label style={{ display: "block", marginBottom: "8px", fontWeight: "bold", color: "#333" }}>
            Operating System *
          </label>
          <input
            type="text"
            name="operatingSystem"
            value={formData.operatingSystem}
            onChange={handleInputChange}
            placeholder="e.g., Windows 11, Ubuntu 20.04, macOS Monterey"
            required
            style={{
              width: "100%",
              padding: "12px",
              border: "1px solid #ddd",
              borderRadius: "4px",
              fontSize: "16px"
            }}
          />
        </div>

        {/* Buttons */}
        <div style={{ display: "flex", gap: "15px", marginTop: "20px" }}>
          <button
            type="button"
            onClick={handleCancel}
            disabled={loading}
            style={{
              flex: 1,
              padding: "15px",
              backgroundColor: "#6c757d",
              color: "white",
              border: "none",
              borderRadius: "4px",
              fontSize: "16px",
              cursor: loading ? "not-allowed" : "pointer"
            }}
          >
            Cancel
          </button>
          
          <button
            type="submit"
            disabled={loading}
            style={{
              flex: 2,
              padding: "15px",
              backgroundColor: loading ? "#ccc" : "#4CAF50",
              color: "white",
              border: "none",
              borderRadius: "4px",
              fontSize: "16px",
              cursor: loading ? "not-allowed" : "pointer"
            }}
          >
            {loading ? "Creating Ticket..." : "Create Ticket & Start Chat"}
          </button>
        </div>
      </form>
    </div>
  );
}
