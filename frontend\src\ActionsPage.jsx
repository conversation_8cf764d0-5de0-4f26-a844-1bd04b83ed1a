import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import "./App.css";

const BACKEND_URL = "http://localhost:8000";

export default function ActionsPage({ token }) {
  const navigate = useNavigate();
  const accessToken = token || localStorage.getItem("access");
  const [loading, setLoading] = useState(false);
  const [pendingTickets, setPendingTickets] = useState([]);
  const [userName, setUserName] = useState("");

  useEffect(() => {
    if (!accessToken) {
      navigate("/auth");
      return;
    }

    // Fetch user info
    fetch(`${BACKEND_URL}/api/user_info/`, {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${accessToken}`,
      },
    })
      .then(async (res) => {
        if (!res.ok) {
          throw new Error("User info fetch failed");
        }
        return res.json();
      })
      .then((data) => {
        setUserName(data.name || data.username || data.email);
      })
      .catch((err) => {
        console.error("Failed to fetch user info:", err);
        localStorage.removeItem("access");
        navigate("/auth");
      });

    // Fetch pending tickets
    fetchPendingTickets();
  }, [accessToken, navigate]);

  const fetchPendingTickets = async () => {
    try {
      const response = await fetch(`${BACKEND_URL}/api/pending_tickets/`, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });
      const data = await response.json();

      if (response.ok) {
        setPendingTickets(data.tickets || []);
      }
    } catch (err) {
      console.error("Error fetching pending tickets:", err);
      setPendingTickets([]);
    }
  };

  const handleRaiseNewTicket = () => {
    navigate("/new-ticket");
  };

  const handleUsePendingTicket = () => {
    if (pendingTickets.length === 0) {
      alert("No pending tickets found. Please create a new ticket.");
      return;
    }

    if (pendingTickets.length === 1) {
      // Directly redirect to chatbot with the single ticket
      navigate(`/chatbot/${pendingTickets[0].ticket_number}`);
    } else {
      // Show selection modal/page for multiple tickets
      navigate("/select-ticket");
    }
  };

  const handleGeneralQueries = () => {
    navigate("/chatbot/general");
  };

  return (
    <div style={{ 
      padding: "40px", 
      maxWidth: "800px", 
      margin: "0 auto", 
      textAlign: "center",
      fontFamily: "Arial, sans-serif"
    }}>
      <h1 style={{ 
        color: "#333", 
        marginBottom: "20px",
        fontSize: "2.5rem"
      }}>
        Welcome, {userName}!
      </h1>
      
      <p style={{ 
        fontSize: "1.2rem", 
        color: "#666", 
        marginBottom: "40px",
        lineHeight: "1.6"
      }}>
        How can we help you today? Please choose one of the options below:
      </p>

      <div style={{ 
        display: "flex", 
        flexDirection: "column", 
        gap: "20px", 
        alignItems: "center" 
      }}>
        
        {/* Raise New Ticket Button */}
        <button
          onClick={handleRaiseNewTicket}
          disabled={loading}
          style={{
            backgroundColor: "#4CAF50",
            color: "white",
            border: "none",
            padding: "20px 40px",
            fontSize: "1.3rem",
            borderRadius: "8px",
            cursor: loading ? "not-allowed" : "pointer",
            width: "300px",
            boxShadow: "0 4px 8px rgba(0,0,0,0.1)",
            transition: "all 0.3s ease",
          }}
          onMouseOver={(e) => {
            if (!loading) {
              e.target.style.backgroundColor = "#45a049";
              e.target.style.transform = "translateY(-2px)";
            }
          }}
          onMouseOut={(e) => {
            if (!loading) {
              e.target.style.backgroundColor = "#4CAF50";
              e.target.style.transform = "translateY(0)";
            }
          }}
        >
          🎫 Raise New Ticket
        </button>

        {/* Use Pending Ticket Button */}
        <button
          onClick={handleUsePendingTicket}
          disabled={loading}
          style={{
            backgroundColor: pendingTickets.length > 0 ? "#2196F3" : "#ccc",
            color: "white",
            border: "none",
            padding: "20px 40px",
            fontSize: "1.3rem",
            borderRadius: "8px",
            cursor: loading || pendingTickets.length === 0 ? "not-allowed" : "pointer",
            width: "300px",
            boxShadow: "0 4px 8px rgba(0,0,0,0.1)",
            transition: "all 0.3s ease",
          }}
          onMouseOver={(e) => {
            if (!loading && pendingTickets.length > 0) {
              e.target.style.backgroundColor = "#1976D2";
              e.target.style.transform = "translateY(-2px)";
            }
          }}
          onMouseOut={(e) => {
            if (!loading && pendingTickets.length > 0) {
              e.target.style.backgroundColor = "#2196F3";
              e.target.style.transform = "translateY(0)";
            }
          }}
        >
          📋 Use Pending Ticket ({pendingTickets.length})
        </button>

        {/* General Queries Button */}
        <button
          onClick={handleGeneralQueries}
          disabled={loading}
          style={{
            backgroundColor: "#FF9800",
            color: "white",
            border: "none",
            padding: "20px 40px",
            fontSize: "1.3rem",
            borderRadius: "8px",
            cursor: loading ? "not-allowed" : "pointer",
            width: "300px",
            boxShadow: "0 4px 8px rgba(0,0,0,0.1)",
            transition: "all 0.3s ease",
          }}
          onMouseOver={(e) => {
            if (!loading) {
              e.target.style.backgroundColor = "#F57C00";
              e.target.style.transform = "translateY(-2px)";
            }
          }}
          onMouseOut={(e) => {
            if (!loading) {
              e.target.style.backgroundColor = "#FF9800";
              e.target.style.transform = "translateY(0)";
            }
          }}
        >
          💬 General Queries
        </button>
      </div>

      {pendingTickets.length > 0 && (
        <div style={{ 
          marginTop: "30px", 
          padding: "20px", 
          backgroundColor: "#f5f5f5", 
          borderRadius: "8px",
          textAlign: "left"
        }}>
          <h3 style={{ color: "#333", marginBottom: "15px" }}>Your Pending Tickets:</h3>
          {pendingTickets.map((ticket, index) => (
            <div key={index} style={{ 
              marginBottom: "10px", 
              padding: "10px", 
              backgroundColor: "white", 
              borderRadius: "4px",
              border: "1px solid #ddd"
            }}>
              <strong>#{ticket.ticket_number}</strong> - {ticket.issue}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
